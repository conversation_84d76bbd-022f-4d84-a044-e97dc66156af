{"name": "wrjgl", "version": "3.8.5", "description": "无人机管理系统", "author": "wrjgl", "license": "AGPL-3.0", "scripts": {"dev": "vue-cli-service serve", "build:prod": "vue-cli-service build", "build:stage": "vue-cli-service build --mode staging", "preview": "node build/index.js --preview", "lint": "eslint --ext .js,.vue src"}, "husky": {"hooks": {"pre-commit": "lint-staged"}}, "lint-staged": {"src/**/*.{js,vue}": ["eslint --fix", "git add"]}, "keywords": ["vue", "admin", "dashboard", "element-ui", "boilerplate", "admin-template", "management-system"], "repository": {"type": "git", "url": ""}, "dependencies": {"@amap/amap-jsapi-loader": "^1.0.1", "@easydarwin/easywasmplayer": "^4.0.13", "@open-wc/webpack-import-meta-loader": "^0.4.7", "@riophae/vue-treeselect": "0.4.0", "agora-rtc-sdk-ng": "^4.17.2", "axios": "0.24.0", "clipboard": "2.0.8", "core-js": "^3.25.3", "crypto-js": "^4.1.1", "echarts": "5.4.0", "element-ui": "2.15.12", "file-saver": "2.0.5", "flv.js": "^1.6.2", "fuse.js": "6.4.3", "highlight.js": "9.18.5", "html2canvas": "^1.4.1", "js-audio-recorder": "^1.0.7", "js-beautify": "1.13.0", "js-cookie": "3.0.1", "jsencrypt": "3.0.0-rc.1", "minio": "^7.1.1", "mqtt": "^4.3.7", "nprogress": "0.2.0", "qrcodejs2": "^0.0.2", "quill": "1.3.7", "screenfull": "5.0.2", "sortablejs": "1.10.2", "video-js": "^0.7.0", "videojs-flvjs-es6": "^1.0.1", "vue": "2.6.12", "vue-count-to": "1.0.13", "vue-cropper": "0.5.5", "vue-meta": "2.4.0", "vue-router": "3.4.9", "vuedraggable": "2.24.3", "vuex": "3.6.0"}, "devDependencies": {"@vue/cli-plugin-babel": "4.4.6", "@vue/cli-plugin-eslint": "4.4.6", "@vue/cli-service": "4.4.6", "babel-eslint": "10.1.0", "babel-plugin-dynamic-import-node": "2.3.3", "babel-plugin-transform-remove-strict-mode": "^0.0.2", "chalk": "4.1.0", "compression-webpack-plugin": "5.0.2", "connect": "3.6.6", "eslint": "7.15.0", "eslint-plugin-vue": "7.2.0", "lint-staged": "10.5.3", "runjs": "4.4.2", "sass": "1.32.13", "sass-loader": "10.1.1", "script-ext-html-webpack-plugin": "2.1.5", "svg-sprite-loader": "5.1.1", "vue-template-compiler": "2.6.12"}, "engines": {"node": ">=8.9", "npm": ">= 3.0.0"}, "browserslist": ["> 1%", "last 2 versions"]}