<template>
  <div class="app-container">
    <el-form :model="queryParams" ref="queryForm" size="small" :inline="true" v-show="showSearch">
      <el-form-item label="景点名称" prop="scenicSpotName">
        <el-input
          v-model="queryParams.scenicSpotName"
          placeholder="请输入景点名称"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <!-- <el-form-item label="景点图片banner" prop="banner">
        <el-input
          v-model="queryParams.banner"
          placeholder="请输入景点图片banner"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item> -->
      <el-form-item>
        <el-button type="primary" icon="el-icon-search" size="mini" @click="handleQuery">搜索</el-button>
        <el-button icon="el-icon-refresh" size="mini" @click="resetQuery">重置</el-button>
      </el-form-item>
    </el-form>

    <el-row :gutter="10" class="mb8">
      <el-col :span="1.5">
        <el-button
          type="primary"
          plain
          icon="el-icon-plus"
          size="mini"
          @click="handleAdd"
          v-hasPermi="['business:spot:add']"
        >新增</el-button>
      </el-col>
      <!-- <el-col :span="1.5">
        <el-button
          type="success"
          plain
          icon="el-icon-edit"
          size="mini"
          :disabled="single"
          @click="handleUpdate"
          v-hasPermi="['business:spot:edit']"
        >修改</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button
          type="danger"
          plain
          icon="el-icon-delete"
          size="mini"
          :disabled="multiple"
          @click="handleDelete"
          v-hasPermi="['business:spot:remove']"
        >删除</el-button>
      </el-col> -->
      <el-col :span="1.5">
        <el-button
          type="warning"
          plain
          icon="el-icon-download"
          size="mini"
          @click="handleExport"
          v-hasPermi="['business:spot:export']"
        >导出</el-button>
      </el-col>
      <right-toolbar :showSearch.sync="showSearch" @queryTable="getList"></right-toolbar>
    </el-row>

    <el-table v-loading="loading" :data="spotList">
      <!-- <el-table-column type="selection" width="55" align="center" />
      <el-table-column label="id" align="center" prop="id" /> -->
      <el-table-column label="景点名称" align="center" prop="scenicSpotName" />
      <el-table-column label="景点图标" align="center" prop="iconUrl" width="80">
        <template slot-scope="scope">
          <image-preview :src="scope.row.iconUrl" :width="40" :height="40"/>
        </template>
      </el-table-column>
      <el-table-column label="景点详情图片" align="center" prop="banner" width="100">
        <template slot-scope="scope">
          <image-preview :src="scope.row.banner" :width="50" :height="50"/>
        </template>
      </el-table-column>
      <el-table-column label="备注" align="center" prop="remark" />
      <el-table-column label="操作" align="center" class-name="small-padding fixed-width">
        <template slot-scope="scope">
          <el-button
            size="mini"
            type="text"
            icon="el-icon-edit"
            @click="handleUpdate(scope.row)"
            v-hasPermi="['business:spot:edit']"
          >修改</el-button>
          <el-button
            size="mini"
            type="text"
            icon="el-icon-delete"
            @click="handleDelete(scope.row)"
            v-hasPermi="['business:spot:remove']"
          >删除</el-button>
        </template>
      </el-table-column>
    </el-table>
    
    <pagination
      v-show="total>0"
      :total="total"
      :page.sync="queryParams.pageNum"
      :limit.sync="queryParams.pageSize"
      @pagination="getList"
    />

    <!-- 添加或修改景点管理对话框 -->
    <el-dialog :title="title" :visible.sync="open" width="800px" append-to-body>
      <el-form ref="form" :model="form" :rules="rules" label-width="120px">
        <el-form-item label="景点名称" prop="scenicSpotName">
          <el-input v-model="form.scenicSpotName" placeholder="请输入景点名称" />
        </el-form-item>
        <el-form-item label="选择机场" prop="airportIds">
          <el-select
            v-model="form.airportIds"
            multiple
            style="width: 100%"
            placeholder="请选择机场"
            clearable
          >
            <el-option
              v-for="item in airportList"
              :key="item.airportId"
              :label="item.airportName"
              :value="item.airportId"
            />
          </el-select>
        </el-form-item>
        <el-form-item label="选择景区经纬度" prop="coordinates">
          <div class="coordinate-selector">
            <div class="map-container" style="position: relative;">
              <div id="spot-map" style="width: 100%; height: 300px; border: 1px solid #dcdfe6; border-radius: 4px;"></div>
              <!-- 地图右上角搜索框 -->
              <div class="map-search-box">
                <el-select
                  v-model="searchKeyword"
                  filterable
                  remote
                  reserve-keyword
                  placeholder="搜索地理位置"
                  :remote-method="searchLocation"
                  :loading="searchLoading"
                  @change="selectSearchResult"
                  clearable
                  size="small"
                  style="width: 200px;"
                >
                  <el-option
                    v-for="item in searchOptions"
                    :key="item.id"
                    :label="item.name"
                    :value="item.id"
                  >
                  </el-option>
                </el-select>
              </div>
            </div>
            <div class="coordinate-display" v-if="form.latitude && form.longitude" style="margin-top: 10px;">
              <el-row :gutter="5">
                <el-col :span="12">
                  <el-form-item label="纬度" label-width="40px">
                    <el-input v-model="form.latitude" disabled placeholder="请在地图上选择位置" />
                  </el-form-item>
                </el-col>
                <el-col :span="12">
                  <el-form-item label="经度" label-width="40px">
                    <el-input v-model="form.longitude" disabled placeholder="请在地图上选择位置" />
                  </el-form-item>
                </el-col>
              </el-row>
            </div>
          </div>
        </el-form-item>
        <el-form-item label="景点图标" prop="iconUrl">
          <image-upload v-model="form.iconUrl" :limit="1"/>
        </el-form-item>
        <el-form-item label="景点详情图片" prop="banner">
          <image-upload v-model="form.banner" :limit="1"/>
        </el-form-item>
        <el-form-item label="拍照模式" prop="tbShootingModeList">
          <el-button type="primary" size="mini" icon="el-icon-plus" @click="addShootingMode">拍照模式</el-button>
          <div v-for="(item, index) in form.tbShootingModeList" :key="index" class="shooting-mode-item">
            <div class="mode-header">
              <span class="mode-title">拍照模式{{index+1}}</span>
              <el-button type="danger" icon="el-icon-delete" size="mini" @click="removeShootingMode(index)">删除</el-button>
            </div>
            
            <el-form-item label="模式名称" :prop="'tbShootingModeList.' + index + '.modeName'" :rules="{ required: true, message: '模式名称不能为空', trigger: 'blur' }">
              <el-input v-model="item.modeName" placeholder="请输入模式名称"></el-input>
            </el-form-item>
            
            <el-row :gutter="20" style="margin: 10px 0;">
              <el-col :span="12">
                <el-form-item label="是否推荐" :prop="'tbShootingModeList.' + index + '.isRecommend'" :rules="{ required: true, message: '请选择是否推荐', trigger: 'change' }">
                  <el-radio-group v-model="item.isRecommend">
                    <el-radio :label="'1'">是</el-radio>
                    <el-radio :label="'0'">否</el-radio>
                  </el-radio-group>
                </el-form-item>
              </el-col>
              <el-col :span="12">
                <el-form-item label="价格" :prop="'tbShootingModeList.' + index + '.price'" :rules="{ required: true, message: '价格不能为空', trigger: 'blur' }">
                  <el-input-number v-model="item.price" :min="0" controls-position="right" style="width: 100%" placeholder="请输入价格"></el-input-number>
                </el-form-item>
              </el-col>
            </el-row>
            
            <el-row :gutter="20" style="margin: 10px 0;">
              <el-col :span="12">
                <el-form-item label="预计时长" :prop="'tbShootingModeList.' + index + '.estimateTime'" :rules="{ required: true, message: '预计时长不能为空', trigger: 'blur' }">
                  <el-input-number v-model="item.estimateTime" :min="1" controls-position="right" style="width: calc(100% - 60px)" placeholder="请输入预计时长"></el-input-number>
                  <span style="margin-left: 5px;">分钟</span>
                </el-form-item>
              </el-col>
              <el-col :span="12">
                <el-form-item label="航线" :prop="'tbShootingModeList.' + index + '.routesId'" :rules="{ required: true, message: '请选择航线', trigger: 'change' }">
                  <el-select v-model="item.routesId" placeholder="请选择航线" style="width: 100%">
                    <el-option 
                      v-for="route in routesList"
                      :key="route.routesId"
                      :label="route.routesName"
                      :value="route.routesId">
                    </el-option>
                  </el-select>
                </el-form-item>
              </el-col>
            </el-row>
          </div>
          <div v-if="form.tbShootingModeList.length === 0" class="empty-mode-tip">
            <el-alert title="请至少添加一种拍照模式" type="info" show-icon :closable="false"></el-alert>
          </div>
        </el-form-item>
        <el-form-item label="备注" prop="remark">
          <el-input v-model="form.remark" placeholder="请输入备注" />
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button type="primary" @click="submitForm">确 定</el-button>
        <el-button @click="cancel">取 消</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import { listSpot, getSpot, delSpot, addSpot, updateSpot } from "@/api/business/spot";
import { listRoutes, listAirport } from "@/api/business/spot";
import AMapLoader from "@amap/amap-jsapi-loader";
import { amapKey, amapSecretkey } from "@/utils/use-g-map";

export default {
  name: "Spot",
  data() {
    return {
      // 遮罩层
      loading: true,
      // 选中数组
      ids: [],
      // 非单个禁用
      single: true,
      // 非多个禁用
      multiple: true,
      // 显示搜索条件
      showSearch: true,
      // 总条数
      total: 0,
      // 景点管理表格数据
      spotList: [],
      // 弹出层标题
      title: "",
      // 是否显示弹出层
      open: false,
      // 查询参数
      queryParams: {
        pageNum: 1,
        pageSize: 10,
        scenicSpotName: null,
        banner: null,
      },
      // 表单参数
      form: {
        id: null,
        scenicSpotName: null,
        iconUrl: null,
        banner: null,
        tbShootingModeList: [],
        airportIds: [],
        latitude: null,
        longitude: null,
        createBy: null,
        createTime: null,
        updateBy: null,
        updateTime: null,
        remark: null,
      },
      // 航线列表
      routesList: [],
      // 机场列表
      airportList: [],
      // 地图相关
      map: null,
      mapMarker: null,
      geocoder: null,
      placeSearch: null,
      // 搜索相关
      searchKeyword: '',
      searchOptions: [],
      searchLoading: false,
      // 是否需要显示已有位置
      needShowExistingLocation: false,
      // 表单校验
      rules: {
        scenicSpotName: [
          { required: true, message: "景点名称不能为空", trigger: "blur" }
        ],
        airportIds: [
          { required: true, message: "请选择机场", trigger: "change" }
        ],
        iconUrl: [
          { required: true, message: "景点图标不能为空", trigger: "blur" }
        ],
        banner: [
          { required: true, message: "景点详情图片不能为空", trigger: "blur" }
        ],
        tbShootingModeList: [
          {
            required: true,
            validator: (rule, value, callback) => {
              if (value.length === 0) {
                callback(new Error('请至少添加一种拍照模式'));
              } else {
                callback();
              }
            },
            trigger: 'change'
          }
        ],
        coordinates: [
          {
            required: true,
            validator: (rule, value, callback) => {
              if (!this.form.latitude || !this.form.longitude) {
                callback(new Error('请在地图上选择景区位置'));
              } else {
                callback();
              }
            },
            trigger: 'change'
          }
        ]
      }
    };
  },
  created() {
    this.getList();
    this.getRoutesList();
    this.getAirportList();
  },
  methods: {
    /** 查询景点管理列表 */
    getList() {
      this.loading = true;
      listSpot(this.queryParams).then(response => {
        this.spotList = response.rows;
        this.total = response.total;
        this.loading = false;
      });
    },
    /** 获取航线列表 */
    getRoutesList() {
      listRoutes().then(response => {
        this.routesList = response.rows;
      });
    },
    /** 获取机场列表 */
    getAirportList() {
      listAirport().then(response => {
        this.airportList = response.rows || [];
      });
    },
    /** 提交按钮 */
    submitForm() {
      this.$refs["form"].validate(valid => {
        if (valid) {
          if (this.form.tbShootingModeList.length === 0) {
            this.$message.error("请至少添加一种拍照模式");
            return;
          }
          
          if (this.form.id != null) {
            updateSpot(this.form).then(response => {
              this.$modal.msgSuccess("修改成功");
              this.open = false;
              this.getList();
            });
          } else {
            addSpot(this.form).then(response => {
              this.$modal.msgSuccess("新增成功");
              this.open = false;
              this.getList();
            });
          }
        }
      });
    },
    // 取消按钮
    cancel() {
      this.open = false;
      this.resetForm("form");
    },
    // 表单重置
    reset() {
      this.form = {
        id: null,
        scenicSpotName: null,
        iconUrl: null,
        banner: null,
        tbShootingModeList: [],
        airportIds: [],
        latitude: null,
        longitude: null,
        createBy: null,
        createTime: null,
        updateBy: null,
        updateTime: null,
        remark: null,
      };
      this.resetForm("form");
      // 清空搜索相关数据
      this.searchKeyword = '';
      this.searchOptions = [];
      this.needShowExistingLocation = false;
    },
    /** 搜索按钮操作 */
    handleQuery() {
      this.queryParams.pageNum = 1;
      this.getList();
    },
    /** 重置按钮操作 */
    resetQuery() {
      this.resetForm("queryForm");
      this.handleQuery();
    },
    // 多选框选中数据
    handleSelectionChange(selection) {
      this.ids = selection.map(item => item.id)
      this.single = selection.length!==1
      this.multiple = !selection.length
    },
    /** 新增按钮操作 */
    handleAdd() {
      this.reset();
      this.open = true;
      this.title = "添加景点管理";
    },
    /** 修改按钮操作 */
    handleUpdate(row) {
      this.reset();
      const id = row.id || this.ids
      getSpot(id).then(response => {
        this.form = response.data;
        this.open = true;
        this.title = "修改景点管理";
        // 标记需要显示已有位置，在地图初始化完成后处理
        this.needShowExistingLocation = true;
      });
    },
    /** 删除按钮操作 */
    handleDelete(row) {
      const ids = row.id || this.ids;
      this.$modal.confirm('是否确认删除景点管理编号为"' + ids + '"的数据项？').then(function() {
        return delSpot(ids);
      }).then(() => {
        this.getList();
        this.$modal.msgSuccess("删除成功");
      }).catch(() => {});
    },
    /** 添加拍照模式 */
    addShootingMode() {
      this.form.tbShootingModeList.push({
        modeName: "",
        price: 0,
        isRecommend: "0",
        routesId: "",
        estimateTime: 5
      });
    },
    /** 删除拍照模式 */
    removeShootingMode(index) {
      this.form.tbShootingModeList.splice(index, 1);
    },
    /** 导出按钮操作 */
    handleExport() {
      this.download('business/spot/export', {
        ...this.queryParams
      }, `spot_${new Date().getTime()}.xlsx`)
    },
    /** 初始化地图 */
    initMap() {
      // 如果地图已存在，先销毁
      if (this.map) {
        this.destroyMap();
      }

      // 检查地图容器是否存在
      const mapContainer = document.getElementById('spot-map');
      if (!mapContainer) {
        console.error('地图容器不存在');
        return;
      }

      let _this = this;
      window._AMapSecurityConfig = {
        securityJsCode: amapSecretkey
      }
      AMapLoader.load({
        key: amapKey,
        version: "2.0",
        plugins: [
          "AMap.Geocoder",
          "AMap.PlaceSearch"
        ],
      })
      .then((AMap) => {
        _this.map = new AMap.Map("spot-map", {
          zoom: 12,
          mapStyle: "amap://styles/whitesmoke",
          center: [113.798416, 34.748949], // 北京天安门坐标
        });
        _this.geocoder = new AMap.Geocoder({
          radius: 1000 //范围，默认：500
        });
        _this.placeSearch = new AMap.PlaceSearch({
          city: "全国"
        });
        _this.map.on("click", _this.mapClick);

        // 地图初始化完成后，检查是否需要显示已有位置
        _this.map.on('complete', () => {
          console.log('地图初始化完成');
          if (_this.needShowExistingLocation && _this.form.latitude && _this.form.longitude) {
            console.log('检测到需要显示已有经纬度，准备显示位置');
            setTimeout(() => {
              _this.showExistingLocation();
              _this.needShowExistingLocation = false;
            }, 300);
          }
        });
      })
      .catch((e) => {
        console.error('地图初始化失败:', e);
        _this.map = null;
      });
    },
    /** 地图点击事件 */
    mapClick(e) {
      let _this = this;
      // 清除之前的标记
      if (this.mapMarker) {
        this.mapMarker.remove();
        this.mapMarker = null;
      }

      // 创建新的标记
      const icon = new AMap.Icon({
        size: new AMap.Size(24, 24),
        image: require("@/assets/images/mapicon/a16.png"),
        imageSize: new AMap.Size(24, 24),
        imageOffset: new AMap.Pixel(0, 0),
      });
      this.mapMarker = new AMap.Marker({
        position: [e.lnglat.lng, e.lnglat.lat],
        icon: icon,
        offset: new AMap.Pixel(-12, -12),
      });
      this.map.add(this.mapMarker);

      // 设置经纬度
      this.form.latitude = e.lnglat.lat.toFixed(6);
      this.form.longitude = e.lnglat.lng.toFixed(6);
    },
    /** 显示已有位置 */
    showExistingLocation() {
      if (!this.map || !this.form.latitude || !this.form.longitude) {
        console.log('地图或经纬度数据不存在，无法显示位置');
        return;
      }

      console.log('显示已有位置:', this.form.latitude, this.form.longitude);

      // 清除之前的标记
      if (this.mapMarker) {
        this.mapMarker.remove();
        this.mapMarker = null;
      }

      // 转换经纬度为数字类型
      const lat = parseFloat(this.form.latitude);
      const lng = parseFloat(this.form.longitude);

      // 创建标记
      const icon = new AMap.Icon({
        size: new AMap.Size(24, 24),
        image: require("@/assets/images/mapicon/a16.png"),
        imageSize: new AMap.Size(24, 24),
        imageOffset: new AMap.Pixel(0, 0),
      });
      this.mapMarker = new AMap.Marker({
        position: [lng, lat],
        icon: icon,
        offset: new AMap.Pixel(-12, -12),
      });
      this.map.add(this.mapMarker);

      // 设置地图中心和缩放级别
      this.map.setZoomAndCenter(15, [lng, lat]);
      console.log('已在地图上显示位置标记');
    },
    /** 搜索地理位置 */
    searchLocation(keyword) {
      if (!keyword || !this.placeSearch) {
        this.searchOptions = [];
        return;
      }

      this.searchLoading = true;
      this.placeSearch.search(keyword, (status, result) => {
        this.searchLoading = false;
        if (status === 'complete' && result.poiList && result.poiList.pois) {
          this.searchOptions = result.poiList.pois.map((poi, index) => ({
            id: `${poi.location.lng},${poi.location.lat}`,
            name: poi.name + ' - ' + poi.address,
            location: poi.location
          }));
        } else {
          this.searchOptions = [];
        }
      });
    },
    /** 选择搜索结果 */
    selectSearchResult(value) {
      if (!value) return;

      const selectedOption = this.searchOptions.find(option => option.id === value);
      if (selectedOption) {
        const { lng, lat } = selectedOption.location;

        // 清除之前的标记
        if (this.mapMarker) {
          this.mapMarker.remove();
          this.mapMarker = null;
        }

        // 创建新的标记
        const icon = new AMap.Icon({
          size: new AMap.Size(24, 24),
          image: require("@/assets/images/mapicon/a16.png"),
          imageSize: new AMap.Size(24, 24),
          imageOffset: new AMap.Pixel(0, 0),
        });
        this.mapMarker = new AMap.Marker({
          position: [lng, lat],
          icon: icon,
          offset: new AMap.Pixel(-12, -12),
        });
        this.map.add(this.mapMarker);

        // 设置经纬度
        this.form.latitude = lat.toFixed(6);
        this.form.longitude = lng.toFixed(6);

        // 设置地图中心和缩放级别
        this.map.setZoomAndCenter(15, [lng, lat]);
      }
    },
    /** 销毁地图 */
    destroyMap() {
      if (this.mapMarker) {
        this.mapMarker.remove();
        this.mapMarker = null;
      }
      if (this.map) {
        this.map.destroy();
        this.map = null;
      }
      this.geocoder = null;
      this.placeSearch = null;
    }
  },
  watch: {
    // 监听弹窗关闭，清除表单验证
    open(val) {
      if (!val) {
        // 弹窗关闭时，重置表单验证和清理地图
        if (this.$refs.form) {
          this.$nextTick(() => {
            this.$refs.form.clearValidate();
          });
        }
        // 清理地图实例
        this.destroyMap();
      } else {
        // 弹窗打开时，延迟初始化地图
        this.$nextTick(() => {
          // 确保DOM已经渲染完成
          setTimeout(() => {
            this.initMap();
          }, 100);
        });
      }
    }
  }
};
</script>

<style scoped>
.shooting-mode-item {
  border: 1px solid #ebeef5;
  padding: 18px;
  margin-bottom: 18px;
  border-radius: 4px;
  background-color: #fafafa;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
}
.mode-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 15px;
  border-bottom: 1px solid #ebeef5;
  padding-bottom: 10px;
}
.mode-title {
  font-size: 16px;
  font-weight: bold;
  color: #303133;
}
.mode-row {
  margin-bottom: 10px;
}
.mode-row:last-child {
  margin-bottom: 0;
}
.empty-mode-tip {
  margin-top: 10px;
}
.coordinate-selector {
  width: 100%;
}
.coordinate-display {
  margin-top: 10px;
}
.coordinate-display .el-form-item {
  margin-bottom: 0;
}
.map-search-box {
  position: absolute;
  top: 10px;
  right: 10px;
  z-index: 1000;
  background: rgba(255, 255, 255, 0.9);
  padding: 5px;
  border-radius: 4px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}
</style>
